import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.*;
import java.util.*;
import java.util.List;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * SOC智能研判分析工具 - Java图形化版本
 * 支持JDK 21.0.7
 */
public class SOCAnalyzer extends JFrame {
    private JButton selectFileButton;
    private JButton analyzeButton;
    private JButton saveButton;
    private JLabel filePathLabel;
    private JTextArea resultTextArea;
    private JTable statisticsTable;
    private DefaultTableModel tableModel;
    private JScrollPane tableScrollPane;
    
    private File selectedFile;
    private List<Map<String, Object>> analyzedData;
    private Map<String, Integer> statisticsData;
    
    public SOCAnalyzer() {
        initializeGUI();
    }
    
    private void initializeGUI() {
        setTitle("SOC智能研判分析工具 v1.0");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        
        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout());
        
        // 顶部面板 - 文件选择和操作按钮
        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        selectFileButton = new JButton("选择Excel文件");
        analyzeButton = new JButton("开始分析");
        saveButton = new JButton("保存结果");
        filePathLabel = new JLabel("未选择文件");
        
        analyzeButton.setEnabled(false);
        saveButton.setEnabled(false);
        
        topPanel.add(selectFileButton);
        topPanel.add(analyzeButton);
        topPanel.add(saveButton);
        topPanel.add(new JLabel("  |  "));
        topPanel.add(filePathLabel);
        
        // 中间面板 - 分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        
        // 上半部分 - 统计表格
        String[] columnNames = {"智能研判结论", "分析类型", "记录数量", "占比(%)"};
        tableModel = new DefaultTableModel(columnNames, 0);
        statisticsTable = new JTable(tableModel);
        statisticsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        statisticsTable.getTableHeader().setReorderingAllowed(false);
        tableScrollPane = new JScrollPane(statisticsTable);
        tableScrollPane.setBorder(BorderFactory.createTitledBorder("统计结果"));
        tableScrollPane.setPreferredSize(new Dimension(950, 300));
        
        // 下半部分 - 详细结果文本区域
        resultTextArea = new JTextArea();
        resultTextArea.setEditable(false);
        resultTextArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane textScrollPane = new JScrollPane(resultTextArea);
        textScrollPane.setBorder(BorderFactory.createTitledBorder("详细分析结果"));
        textScrollPane.setPreferredSize(new Dimension(950, 250));
        
        splitPane.setTopComponent(tableScrollPane);
        splitPane.setBottomComponent(textScrollPane);
        splitPane.setDividerLocation(320);
        
        // 底部面板 - 状态栏
        JPanel bottomPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JLabel statusLabel = new JLabel("就绪");
        bottomPanel.add(statusLabel);
        
        // 添加组件到主面板
        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(splitPane, BorderLayout.CENTER);
        mainPanel.add(bottomPanel, BorderLayout.SOUTH);
        
        add(mainPanel);
        
        // 添加事件监听器
        selectFileButton.addActionListener(new SelectFileListener());
        analyzeButton.addActionListener(new AnalyzeListener());
        saveButton.addActionListener(new SaveListener());
    }
    
    // 文件选择监听器
    private class SelectFileListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setFileFilter(new FileNameExtensionFilter("Excel文件 (*.xlsx)", "xlsx"));
            
            int result = fileChooser.showOpenDialog(SOCAnalyzer.this);
            if (result == JFileChooser.APPROVE_OPTION) {
                selectedFile = fileChooser.getSelectedFile();
                filePathLabel.setText(selectedFile.getName());
                analyzeButton.setEnabled(true);
                
                // 清空之前的结果
                tableModel.setRowCount(0);
                resultTextArea.setText("");
                saveButton.setEnabled(false);
            }
        }
    }
    
    // 分析监听器
    private class AnalyzeListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (selectedFile == null) {
                JOptionPane.showMessageDialog(SOCAnalyzer.this, "请先选择Excel文件", "错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            // 在后台线程中执行分析
            SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
                @Override
                protected Void doInBackground() throws Exception {
                    publish("开始分析文件: " + selectedFile.getName());
                    analyzeFile();
                    return null;
                }
                
                @Override
                protected void process(List<String> chunks) {
                    for (String message : chunks) {
                        resultTextArea.append(message + "\n");
                    }
                }
                
                @Override
                protected void done() {
                    try {
                        get(); // 检查是否有异常
                        publish("分析完成！");
                        saveButton.setEnabled(true);
                    } catch (Exception ex) {
                        JOptionPane.showMessageDialog(SOCAnalyzer.this, 
                            "分析过程中出现错误: " + ex.getMessage(), 
                            "错误", JOptionPane.ERROR_MESSAGE);
                        ex.printStackTrace();
                    }
                }
            };
            
            worker.execute();
        }
    }
    
    // 保存监听器
    private class SaveListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (analyzedData == null || analyzedData.isEmpty()) {
                JOptionPane.showMessageDialog(SOCAnalyzer.this, "没有可保存的分析结果", "错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setFileFilter(new FileNameExtensionFilter("Excel文件 (*.xlsx)", "xlsx"));
            
            // 设置默认文件名
            String defaultName = selectedFile.getName().replaceFirst("[.][^.]+$", "") + "_analyzed.xlsx";
            fileChooser.setSelectedFile(new File(defaultName));
            
            int result = fileChooser.showSaveDialog(SOCAnalyzer.this);
            if (result == JFileChooser.APPROVE_OPTION) {
                File saveFile = fileChooser.getSelectedFile();
                if (!saveFile.getName().toLowerCase().endsWith(".xlsx")) {
                    saveFile = new File(saveFile.getAbsolutePath() + ".xlsx");
                }
                
                try {
                    saveAnalyzedData(saveFile);
                    JOptionPane.showMessageDialog(SOCAnalyzer.this, 
                        "分析结果已保存到: " + saveFile.getAbsolutePath(), 
                        "保存成功", JOptionPane.INFORMATION_MESSAGE);
                } catch (Exception ex) {
                    JOptionPane.showMessageDialog(SOCAnalyzer.this, 
                        "保存文件时出现错误: " + ex.getMessage(), 
                        "错误", JOptionPane.ERROR_MESSAGE);
                    ex.printStackTrace();
                }
            }
        }
    }
    
    // 分析文件的核心方法
    private void analyzeFile() throws Exception {
        analyzedData = new ArrayList<>();
        statisticsData = new LinkedHashMap<>();
        
        try (FileInputStream fis = new FileInputStream(selectedFile);
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            
            // 查找列索引
            Row headerRow = sheet.getRow(0);
            int triageResultIndex = -1;
            int triageRuleNameIndex = -1;
            
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    String cellValue = cell.getStringCellValue();
                    if ("triageResult".equals(cellValue)) {
                        triageResultIndex = i;
                    } else if ("triageRuleName".equals(cellValue)) {
                        triageRuleNameIndex = i;
                    }
                }
            }
            
            if (triageResultIndex == -1 || triageRuleNameIndex == -1) {
                throw new Exception("未找到必需的列: triageResult 或 triageRuleName");
            }
            
            // 处理数据行
            int processedCount = 0;
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                Cell resultCell = row.getCell(triageResultIndex);
                Cell strategyCell = row.getCell(triageRuleNameIndex);
                
                if (resultCell == null) continue;
                
                String result = getCellValueAsString(resultCell);
                String strategy = getCellValueAsString(strategyCell);
                
                // 确定分析类型
                String analysisType = determineAnalysisType(strategy);
                
                // 添加标签（如果还没有）
                if (!result.endsWith("[ai分析]") && !result.endsWith("[规则研判]")) {
                    result = result + "[" + analysisType + "]";
                    resultCell.setCellValue(result);
                    processedCount++;
                }
                
                // 保存到分析数据中
                Map<String, Object> rowData = new HashMap<>();
                for (int j = 0; j < headerRow.getLastCellNum(); j++) {
                    Cell cell = headerRow.getCell(j);
                    Cell dataCell = row.getCell(j);
                    if (cell != null) {
                        String columnName = cell.getStringCellValue();
                        String cellValue = getCellValueAsString(dataCell);
                        if (j == triageResultIndex) {
                            cellValue = result; // 使用更新后的结果
                        }
                        rowData.put(columnName, cellValue);
                    }
                }
                analyzedData.add(rowData);
                
                // 统计结论分布
                Integer count = statisticsData.get(result);
                statisticsData.put(result, count == null ? 1 : count + 1);
            }
            
            // 更新GUI
            final int finalProcessedCount = processedCount;
            SwingUtilities.invokeLater(new Runnable() {
                @Override
                public void run() {
                    updateStatisticsTable();
                    updateResultText(finalProcessedCount);
                }
            });
        }
    }
    
    // 确定分析类型
    private String determineAnalysisType(String strategy) {
        if (strategy == null || strategy.trim().isEmpty()) {
            return "规则研判";
        }
        
        String lowerStrategy = strategy.toLowerCase();
        String[] aiKeywords = {"ai", "人工智能", "机器学习", "深度学习", "神经网络", "算法"};
        
        for (String keyword : aiKeywords) {
            if (lowerStrategy.contains(keyword)) {
                return "ai分析";
            }
        }
        
        return "规则研判";
    }
    
    // 获取单元格值作为字符串
    private String getCellValueAsString(Cell cell) {
        if (cell == null) return "";
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
    
    // 更新统计表格
    private void updateStatisticsTable() {
        tableModel.setRowCount(0);
        
        // 按数量排序
        List<Map.Entry<String, Integer>> sortedEntries = new ArrayList<>(statisticsData.entrySet());
        Collections.sort(sortedEntries, new Comparator<Map.Entry<String, Integer>>() {
            @Override
            public int compare(Map.Entry<String, Integer> a, Map.Entry<String, Integer> b) {
                return b.getValue().compareTo(a.getValue());
            }
        });
        
        int totalRecords = analyzedData.size();
        for (Map.Entry<String, Integer> entry : sortedEntries) {
            String conclusion = entry.getKey();
            int count = entry.getValue();
            String analysisType = conclusion.contains("[ai分析]") ? "AI分析" : "规则研判";
            double percentage = (double) count / totalRecords * 100;
            
            Object[] row = {conclusion, analysisType, count, String.format("%.2f", percentage)};
            tableModel.addRow(row);
        }
    }
    
    // 更新结果文本
    private void updateResultText(int processedCount) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 分析完成 ===\n");
        sb.append("文件: ").append(selectedFile.getName()).append("\n");
        sb.append("总记录数: ").append(analyzedData.size()).append("\n");
        sb.append("处理记录数: ").append(processedCount).append("\n\n");
        
        sb.append("=== 智能研判结论统计 ===\n");
        List<Map.Entry<String, Integer>> sortedEntries = new ArrayList<>(statisticsData.entrySet());
        Collections.sort(sortedEntries, new Comparator<Map.Entry<String, Integer>>() {
            @Override
            public int compare(Map.Entry<String, Integer> a, Map.Entry<String, Integer> b) {
                return b.getValue().compareTo(a.getValue());
            }
        });
        
        int aiCount = 0, ruleCount = 0;
        for (Map.Entry<String, Integer> entry : sortedEntries) {
            String conclusion = entry.getKey();
            int count = entry.getValue();
            sb.append(conclusion).append(" 共").append(count).append("条\n");
            
            if (conclusion.contains("[ai分析]")) {
                aiCount += count;
            } else {
                ruleCount += count;
            }
        }
        
        sb.append("\n=== 汇总统计 ===\n");
        sb.append("AI分析: ").append(aiCount).append("条记录\n");
        sb.append("规则研判: ").append(ruleCount).append("条记录\n");
        sb.append("结论种类: ").append(statisticsData.size()).append("种\n");
        
        resultTextArea.setText(sb.toString());
    }
    
    // 保存分析数据
    private void saveAnalyzedData(File file) throws Exception {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("分析结果");
            
            if (analyzedData.isEmpty()) return;
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            Map<String, Object> firstRow = analyzedData.get(0);
            int colIndex = 0;
            String[] columnNames = firstRow.keySet().toArray(new String[0]);
            
            for (String columnName : columnNames) {
                Cell cell = headerRow.createCell(colIndex++);
                cell.setCellValue(columnName);
            }
            
            // 填充数据
            for (int i = 0; i < analyzedData.size(); i++) {
                Row row = sheet.createRow(i + 1);
                Map<String, Object> rowData = analyzedData.get(i);
                colIndex = 0;
                
                for (String columnName : columnNames) {
                    Cell cell = row.createCell(colIndex++);
                    Object value = rowData.get(columnName);
                    if (value != null) {
                        cell.setCellValue(value.toString());
                    }
                }
            }
            
            // 自动调整列宽
            for (int i = 0; i < columnNames.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(file)) {
                workbook.write(fos);
            }
        }
    }
    
    public static void main(String[] args) {
        // 设置系统外观
        try {
            UIManager.setLookAndFeel(UIManager.getSystemLookAndFeel());
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                new SOCAnalyzer().setVisible(true);
            }
        });
    }
}
