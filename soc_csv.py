#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SOC分析程序 - CSV版本
使用方法: python soc_csv.py -f xxx.csv
"""

import argparse
import pandas as pd
import sys
import os


def determine_analysis_type(strategy_value):
    """根据智能研判策略确定分析类型"""
    if pd.isna(strategy_value):
        return "[规则研判]"
    
    strategy_str = str(strategy_value).lower()
    
    # AI相关关键词
    ai_keywords = ["ai", "人工智能", "机器学习", "深度学习", "神经网络", "算法"]
    
    for keyword in ai_keywords:
        if keyword in strategy_str:
            return "[ai分析]"
    
    return "[规则研判]"


def find_columns(df):
    """查找智能研判结论和智能研判策略列"""
    conclusion_col = None
    strategy_col = None
    
    for col in df.columns:
        if "智能研判结论" in str(col):
            conclusion_col = col
        if "智能研判策略" in str(col):
            strategy_col = col
    
    return conclusion_col, strategy_col


def process_file(file_path):
    """处理文件"""
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path, encoding='utf-8')
        print("成功加载文件: {}".format(file_path))
        print("数据行数: {}".format(len(df)))
        
        # 查找列
        conclusion_col, strategy_col = find_columns(df)
        
        if conclusion_col is None:
            print("错误: 未找到'智能研判结论'列")
            print("可用列名: {}".format(list(df.columns)))
            return False
        
        if strategy_col is None:
            print("错误: 未找到'智能研判策略'列")
            print("可用列名: {}".format(list(df.columns)))
            return False
        
        print("找到智能研判结论列: {}".format(conclusion_col))
        print("找到智能研判策略列: {}".format(strategy_col))
        
        # 处理数据
        processed_count = 0
        for index, row in df.iterrows():
            conclusion = row[conclusion_col]
            strategy = row[strategy_col]
            
            if pd.isna(conclusion):
                continue
            
            # 确定分析类型
            analysis_type = determine_analysis_type(strategy)
            
            # 在结论后追加分析类型
            conclusion_str = str(conclusion)
            if not (conclusion_str.endswith("[ai分析]") or conclusion_str.endswith("[规则研判]")):
                new_conclusion = conclusion_str + analysis_type
                df.at[index, conclusion_col] = new_conclusion
                processed_count += 1
        
        print("成功处理 {} 条记录".format(processed_count))
        
        # 保存结果
        base_name = os.path.splitext(file_path)[0]
        output_file = "{}_analyzed.csv".format(base_name)
        df.to_csv(output_file, index=False, encoding='utf-8')
        print("分析结果已保存到: {}".format(output_file))
        
        # 显示结果预览
        print("\n处理结果预览:")
        print("-" * 50)
        for i, row in df.head().iterrows():
            print("结论: {}".format(row[conclusion_col]))
            print("策略: {}".format(row[strategy_col]))
            print("-" * 30)
        
        return True
        
    except Exception as e:
        print("错误: {}".format(str(e)))
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SOC智能研判分析工具 - CSV版本")
    parser.add_argument("-f", "--file", required=True, help="要分析的CSV文件路径")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.file):
        print("错误: 文件 '{}' 不存在".format(args.file))
        sys.exit(1)
    
    if not args.file.lower().endswith('.csv'):
        print("错误: 文件 '{}' 不是CSV文件".format(args.file))
        sys.exit(1)
    
    print("开始SOC智能研判分析...")
    print("=" * 50)
    
    if process_file(args.file):
        print("=" * 50)
        print("分析完成!")
    else:
        print("=" * 50)
        print("分析失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
