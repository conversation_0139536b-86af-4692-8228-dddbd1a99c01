import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import javax.swing.table.DefaultTableModel;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.*;
import java.util.*;
import java.util.List;

/**
 * SOC智能研判分析工具 - Java图形化版本（简化版，不依赖外部库）
 * 支持JDK 21.0.7
 * 注意：此版本使用CSV格式处理，避免Excel依赖
 */
public class SOCAnalyzerSimple extends JFrame {
    private JButton selectFileButton;
    private JButton analyzeButton;
    private JButton saveButton;
    private JLabel filePathLabel;
    private JTextArea resultTextArea;
    private JTable statisticsTable;
    private DefaultTableModel tableModel;
    private JScrollPane tableScrollPane;
    
    private File selectedFile;
    private List<Map<String, String>> analyzedData;
    private Map<String, Integer> statisticsData;
    
    public SOCAnalyzerSimple() {
        initializeGUI();
    }
    
    private void initializeGUI() {
        setTitle("SOC智能研判分析工具 v1.0 (简化版)");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1000, 700);
        setLocationRelativeTo(null);
        
        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout());
        
        // 顶部面板 - 文件选择和操作按钮
        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        selectFileButton = new JButton("选择CSV文件");
        analyzeButton = new JButton("开始分析");
        saveButton = new JButton("保存结果");
        filePathLabel = new JLabel("未选择文件 (支持CSV格式)");
        
        analyzeButton.setEnabled(false);
        saveButton.setEnabled(false);
        
        topPanel.add(selectFileButton);
        topPanel.add(analyzeButton);
        topPanel.add(saveButton);
        topPanel.add(new JLabel("  |  "));
        topPanel.add(filePathLabel);
        
        // 中间面板 - 分割面板
        JSplitPane splitPane = new JSplitPane(JSplitPane.VERTICAL_SPLIT);
        
        // 上半部分 - 统计表格
        String[] columnNames = {"智能研判结论", "分析类型", "记录数量", "占比(%)"};
        tableModel = new DefaultTableModel(columnNames, 0);
        statisticsTable = new JTable(tableModel);
        statisticsTable.setSelectionMode(ListSelectionModel.SINGLE_SELECTION);
        statisticsTable.getTableHeader().setReorderingAllowed(false);
        tableScrollPane = new JScrollPane(statisticsTable);
        tableScrollPane.setBorder(BorderFactory.createTitledBorder("统计结果"));
        tableScrollPane.setPreferredSize(new Dimension(950, 300));
        
        // 下半部分 - 详细结果文本区域
        resultTextArea = new JTextArea();
        resultTextArea.setEditable(false);
        resultTextArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
        JScrollPane textScrollPane = new JScrollPane(resultTextArea);
        textScrollPane.setBorder(BorderFactory.createTitledBorder("详细分析结果"));
        textScrollPane.setPreferredSize(new Dimension(950, 250));
        
        splitPane.setTopComponent(tableScrollPane);
        splitPane.setBottomComponent(textScrollPane);
        splitPane.setDividerLocation(320);
        
        // 底部面板 - 状态栏
        JPanel bottomPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JLabel statusLabel = new JLabel("就绪 - 请选择CSV文件进行分析");
        bottomPanel.add(statusLabel);
        
        // 添加组件到主面板
        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(splitPane, BorderLayout.CENTER);
        mainPanel.add(bottomPanel, BorderLayout.SOUTH);
        
        add(mainPanel);
        
        // 添加事件监听器
        selectFileButton.addActionListener(new SelectFileListener());
        analyzeButton.addActionListener(new AnalyzeListener());
        saveButton.addActionListener(new SaveListener());
    }
    
    // 文件选择监听器
    private class SelectFileListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setFileFilter(new FileNameExtensionFilter("CSV文件 (*.csv)", "csv"));
            
            int result = fileChooser.showOpenDialog(SOCAnalyzerSimple.this);
            if (result == JFileChooser.APPROVE_OPTION) {
                selectedFile = fileChooser.getSelectedFile();
                filePathLabel.setText(selectedFile.getName());
                analyzeButton.setEnabled(true);
                
                // 清空之前的结果
                tableModel.setRowCount(0);
                resultTextArea.setText("");
                saveButton.setEnabled(false);
            }
        }
    }
    
    // 分析监听器
    private class AnalyzeListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (selectedFile == null) {
                JOptionPane.showMessageDialog(SOCAnalyzerSimple.this, "请先选择CSV文件", "错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            // 在后台线程中执行分析
            SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
                @Override
                protected Void doInBackground() throws Exception {
                    publish("开始分析文件: " + selectedFile.getName());
                    analyzeFile();
                    return null;
                }
                
                @Override
                protected void process(List<String> chunks) {
                    for (String message : chunks) {
                        resultTextArea.append(message + "\n");
                    }
                }
                
                @Override
                protected void done() {
                    try {
                        get(); // 检查是否有异常
                        publish("分析完成！");
                        saveButton.setEnabled(true);
                    } catch (Exception ex) {
                        JOptionPane.showMessageDialog(SOCAnalyzerSimple.this, 
                            "分析过程中出现错误: " + ex.getMessage(), 
                            "错误", JOptionPane.ERROR_MESSAGE);
                        ex.printStackTrace();
                    }
                }
            };
            
            worker.execute();
        }
    }
    
    // 保存监听器
    private class SaveListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (analyzedData == null || analyzedData.isEmpty()) {
                JOptionPane.showMessageDialog(SOCAnalyzerSimple.this, "没有可保存的分析结果", "错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setFileFilter(new FileNameExtensionFilter("CSV文件 (*.csv)", "csv"));
            
            // 设置默认文件名
            String defaultName = selectedFile.getName().replaceFirst("[.][^.]+$", "") + "_analyzed.csv";
            fileChooser.setSelectedFile(new File(defaultName));
            
            int result = fileChooser.showSaveDialog(SOCAnalyzerSimple.this);
            if (result == JFileChooser.APPROVE_OPTION) {
                File saveFile = fileChooser.getSelectedFile();
                if (!saveFile.getName().toLowerCase().endsWith(".csv")) {
                    saveFile = new File(saveFile.getAbsolutePath() + ".csv");
                }
                
                try {
                    saveAnalyzedData(saveFile);
                    JOptionPane.showMessageDialog(SOCAnalyzerSimple.this, 
                        "分析结果已保存到: " + saveFile.getAbsolutePath(), 
                        "保存成功", JOptionPane.INFORMATION_MESSAGE);
                } catch (Exception ex) {
                    JOptionPane.showMessageDialog(SOCAnalyzerSimple.this, 
                        "保存文件时出现错误: " + ex.getMessage(), 
                        "错误", JOptionPane.ERROR_MESSAGE);
                    ex.printStackTrace();
                }
            }
        }
    }
    
    // 分析文件的核心方法
    private void analyzeFile() throws Exception {
        analyzedData = new ArrayList<>();
        statisticsData = new LinkedHashMap<>();
        
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(new FileInputStream(selectedFile), "UTF-8"))) {
            String line;
            String[] headers = null;
            int triageResultIndex = -1;
            int triageRuleNameIndex = -1;
            int processedCount = 0;
            
            // 读取表头
            if ((line = reader.readLine()) != null) {
                headers = parseCsvLine(line);

                // 打印调试信息
                System.out.println("发现的列名:");
                for (int i = 0; i < headers.length; i++) {
                    String cleanHeader = headers[i].trim();
                    headers[i] = cleanHeader; // 清理空格
                    System.out.println("  [" + i + "] '" + cleanHeader + "'");
                }

                // 查找列索引（支持英文和中文列名，忽略大小写和空格）
                for (int i = 0; i < headers.length; i++) {
                    String header = headers[i].trim().toLowerCase();
                    // 检查triageResult列（支持英文和中文）
                    if ("triageresult".equals(header) || "智能研判结论".equals(headers[i].trim())) {
                        triageResultIndex = i;
                        System.out.println("找到triageResult列，索引: " + i + ", 列名: '" + headers[i] + "'");
                    }
                    // 检查triageRuleName列（支持英文和中文）
                    else if ("triagerulename".equals(header) || "智能研判策略".equals(headers[i].trim())) {
                        triageRuleNameIndex = i;
                        System.out.println("找到triageRuleName列，索引: " + i + ", 列名: '" + headers[i] + "'");
                    }
                }

                // 如果第一行没找到需要的列，检查第二行是否是中文表头
                if (triageResultIndex == -1 || triageRuleNameIndex == -1) {
                    System.out.println("第一行未找到所需列，检查第二行...");
                    String secondLine = reader.readLine();
                    if (secondLine != null) {
                        String[] secondHeaders = parseCsvLine(secondLine);
                        System.out.println("第二行列名:");
                        for (int i = 0; i < secondHeaders.length; i++) {
                            String cleanHeader = secondHeaders[i].trim();
                            System.out.println("  [" + i + "] '" + cleanHeader + "'");

                            // 检查第二行的列名
                            if (triageResultIndex == -1 && "智能研判结论".equals(cleanHeader)) {
                                triageResultIndex = i;
                                headers = secondHeaders; // 使用第二行作为表头
                                System.out.println("在第二行找到智能研判结论列，索引: " + i);
                            }
                            if (triageRuleNameIndex == -1 && "智能研判策略".equals(cleanHeader)) {
                                triageRuleNameIndex = i;
                                headers = secondHeaders; // 使用第二行作为表头
                                System.out.println("在第二行找到智能研判策略列，索引: " + i);
                            }
                        }
                    }
                }

                if (triageResultIndex == -1 || triageRuleNameIndex == -1) {
                    StringBuilder errorMsg = new StringBuilder("未找到必需的列:\n");
                    if (triageResultIndex == -1) {
                        errorMsg.append("- 缺少 'triageResult' 或 '智能研判结论' 列\n");
                    }
                    if (triageRuleNameIndex == -1) {
                        errorMsg.append("- 缺少 'triageRuleName' 或 '智能研判策略' 列\n");
                    }
                    errorMsg.append("可用的列名: ");
                    for (int i = 0; i < headers.length; i++) {
                        errorMsg.append("'").append(headers[i]).append("'");
                        if (i < headers.length - 1) errorMsg.append(", ");
                    }
                    throw new Exception(errorMsg.toString());
                }
            }
            
            // 处理数据行
            while ((line = reader.readLine()) != null) {
                String[] values = parseCsvLine(line);
                if (values.length <= Math.max(triageResultIndex, triageRuleNameIndex)) {
                    continue;
                }

                String result = values[triageResultIndex];
                String strategy = values[triageRuleNameIndex];

                if (result == null || result.trim().isEmpty()) {
                    continue;
                }

                // 确定分析类型
                String analysisType = determineAnalysisType(strategy);

                // 添加标签（如果还没有）
                if (!result.endsWith("[ai分析]") && !result.endsWith("[规则研判]")) {
                    result = result + "[" + analysisType + "]";
                    values[triageResultIndex] = result;
                    processedCount++;
                }

                // 保存到分析数据中
                Map<String, String> rowData = new HashMap<>();
                for (int i = 0; i < Math.min(headers.length, values.length); i++) {
                    rowData.put(headers[i], values[i]);
                }
                analyzedData.add(rowData);

                // 统计结论分布
                Integer count = statisticsData.get(result);
                statisticsData.put(result, count == null ? 1 : count + 1);
            }

            // 更新GUI
            final int finalProcessedCount = processedCount;
            SwingUtilities.invokeLater(new Runnable() {
                @Override
                public void run() {
                    updateStatisticsTable();
                    updateResultText(finalProcessedCount);
                }
            });
        }
    }
    
    // 解析CSV行
    private String[] parseCsvLine(String line) {
        List<String> result = new ArrayList<>();
        boolean inQuotes = false;
        StringBuilder current = new StringBuilder();
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                result.add(current.toString());
                current = new StringBuilder();
            } else {
                current.append(c);
            }
        }
        
        result.add(current.toString());
        return result.toArray(new String[0]);
    }
    
    // 确定分析类型
    private String determineAnalysisType(String strategy) {
        if (strategy == null || strategy.trim().isEmpty()) {
            return "规则研判";
        }
        
        String lowerStrategy = strategy.toLowerCase();
        String[] aiKeywords = {"ai", "人工智能", "机器学习", "深度学习", "神经网络", "算法"};
        
        for (String keyword : aiKeywords) {
            if (lowerStrategy.contains(keyword)) {
                return "ai分析";
            }
        }
        
        return "规则研判";
    }
    
    // 更新统计表格
    private void updateStatisticsTable() {
        tableModel.setRowCount(0);
        
        // 按数量排序
        List<Map.Entry<String, Integer>> sortedEntries = new ArrayList<>(statisticsData.entrySet());
        Collections.sort(sortedEntries, new Comparator<Map.Entry<String, Integer>>() {
            @Override
            public int compare(Map.Entry<String, Integer> a, Map.Entry<String, Integer> b) {
                return b.getValue().compareTo(a.getValue());
            }
        });
        
        int totalRecords = analyzedData.size();
        for (Map.Entry<String, Integer> entry : sortedEntries) {
            String conclusion = entry.getKey();
            int count = entry.getValue();
            String analysisType = conclusion.contains("[ai分析]") ? "AI分析" : "规则研判";
            double percentage = (double) count / totalRecords * 100;
            
            Object[] row = {conclusion, analysisType, count, String.format("%.2f", percentage)};
            tableModel.addRow(row);
        }
    }
    
    // 更新结果文本
    private void updateResultText(int processedCount) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 分析完成 ===\n");
        sb.append("文件: ").append(selectedFile.getName()).append("\n");
        sb.append("总记录数: ").append(analyzedData.size()).append("\n");
        sb.append("处理记录数: ").append(processedCount).append("\n\n");
        
        sb.append("=== 智能研判结论统计 ===\n");
        List<Map.Entry<String, Integer>> sortedEntries = new ArrayList<>(statisticsData.entrySet());
        Collections.sort(sortedEntries, new Comparator<Map.Entry<String, Integer>>() {
            @Override
            public int compare(Map.Entry<String, Integer> a, Map.Entry<String, Integer> b) {
                return b.getValue().compareTo(a.getValue());
            }
        });
        
        int aiCount = 0, ruleCount = 0;
        for (Map.Entry<String, Integer> entry : sortedEntries) {
            String conclusion = entry.getKey();
            int count = entry.getValue();
            sb.append(conclusion).append(" 共").append(count).append("条\n");
            
            if (conclusion.contains("[ai分析]")) {
                aiCount += count;
            } else {
                ruleCount += count;
            }
        }
        
        sb.append("\n=== 汇总统计 ===\n");
        sb.append("AI分析: ").append(aiCount).append("条记录\n");
        sb.append("规则研判: ").append(ruleCount).append("条记录\n");
        sb.append("结论种类: ").append(statisticsData.size()).append("种\n");
        
        resultTextArea.setText(sb.toString());
    }
    
    // 保存分析数据
    private void saveAnalyzedData(File file) throws Exception {
        try (PrintWriter writer = new PrintWriter(new OutputStreamWriter(new FileOutputStream(file), "UTF-8"))) {
            if (analyzedData.isEmpty()) return;
            
            // 写入表头
            Map<String, String> firstRow = analyzedData.get(0);
            String[] headers = firstRow.keySet().toArray(new String[0]);
            writer.println(String.join(",", headers));
            
            // 写入数据
            for (Map<String, String> rowData : analyzedData) {
                String[] values = new String[headers.length];
                for (int i = 0; i < headers.length; i++) {
                    String value = rowData.get(headers[i]);
                    values[i] = value != null ? value : "";
                }
                writer.println(String.join(",", values));
            }
        }
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                new SOCAnalyzerSimple().setVisible(true);
            }
        });
    }
}
