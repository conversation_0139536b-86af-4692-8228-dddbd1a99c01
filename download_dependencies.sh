#!/bin/bash

echo "下载Apache POI依赖..."
echo "======================"

# 创建lib目录
mkdir -p lib

# 下载Apache POI相关JAR文件
echo "下载POI核心库..."
curl -L "https://repo1.maven.org/maven2/org/apache/poi/poi/5.2.5/poi-5.2.5.jar" -o "lib/poi-5.2.5.jar"

echo "下载POI OOXML库..."
curl -L "https://repo1.maven.org/maven2/org/apache/poi/poi-ooxml/5.2.5/poi-ooxml-5.2.5.jar" -o "lib/poi-ooxml-5.2.5.jar"

echo "下载POI OOXML Schemas..."
curl -L "https://repo1.maven.org/maven2/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar" -o "lib/poi-ooxml-schemas-4.1.2.jar"

echo "下载Commons Collections..."
curl -L "https://repo1.maven.org/maven2/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar" -o "lib/commons-collections4-4.4.jar"

echo "下载XMLBeans..."
curl -L "https://repo1.maven.org/maven2/org/apache/xmlbeans/xmlbeans/5.1.1/xmlbeans-5.1.1.jar" -o "lib/xmlbeans-5.1.1.jar"

echo "下载Commons Codec..."
curl -L "https://repo1.maven.org/maven2/commons-codec/commons-codec/1.15/commons-codec-1.15.jar" -o "lib/commons-codec-1.15.jar"

echo "下载Commons Compress..."
curl -L "https://repo1.maven.org/maven2/org/apache/commons/commons-compress/1.21/commons-compress-1.21.jar" -o "lib/commons-compress-1.21.jar"

echo "下载SparseBitSet..."
curl -L "https://repo1.maven.org/maven2/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar" -o "lib/SparseBitSet-1.2.jar"

echo "依赖下载完成！"
ls -la lib/
