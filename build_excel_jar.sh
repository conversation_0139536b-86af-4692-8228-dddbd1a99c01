#!/bin/bash

echo "SOC智能研判分析工具 - 构建Excel支持版本"
echo "======================================="

# 检查Java版本
echo "检查Java版本..."
java -version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Java环境"
    exit 1
fi

# 下载依赖（如果不存在）
if [ ! -d "lib" ] || [ ! -f "lib/poi-5.2.5.jar" ]; then
    echo "下载依赖库..."
    chmod +x download_dependencies.sh
    ./download_dependencies.sh
fi

# 创建构建目录
echo "创建构建目录..."
mkdir -p build_excel
mkdir -p dist

# 编译Java文件（包含依赖）
echo "编译Java源文件..."
javac -cp "lib/*" -d build_excel SOCAnalyzer.java

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

# 创建MANIFEST.MF文件
echo "创建MANIFEST.MF文件..."
cat > build_excel/MANIFEST.MF << EOF
Manifest-Version: 1.0
Main-Class: SOCAnalyzer
Class-Path: lib/poi-5.2.5.jar lib/poi-ooxml-5.2.5.jar lib/poi-ooxml-schemas-4.1.2.jar lib/commons-collections4-4.4.jar lib/xmlbeans-5.1.1.jar lib/commons-codec-1.15.jar lib/commons-compress-1.21.jar lib/SparseBitSet-1.2.jar
Created-By: SOC Analysis Tool Builder

EOF

# 解压所有依赖JAR到build目录
echo "解压依赖库..."
cd build_excel
for jar in ../lib/*.jar; do
    echo "解压 $jar"
    jar xf "$jar"
done

# 删除签名文件（避免冲突）
rm -rf META-INF/*.SF META-INF/*.DSA META-INF/*.RSA

cd ..

# 创建包含所有依赖的JAR文件
echo "创建包含依赖的JAR文件..."
cd build_excel
jar cfm ../dist/soc-analyzer-excel.jar MANIFEST.MF *.class org/ com/ javax/ schemaorg_apache_xmlbeans/ 2>/dev/null
cd ..

if [ $? -eq 0 ]; then
    echo "构建成功！"
    echo "JAR文件位置: dist/soc-analyzer-excel.jar"
    echo "运行命令: java -jar dist/soc-analyzer-excel.jar"
    
    # 测试JAR文件
    echo "测试JAR文件..."
    java -jar dist/soc-analyzer-excel.jar --version 2>/dev/null || echo "JAR文件创建成功，可以运行！"
else
    echo "创建JAR文件失败！"
    exit 1
fi

echo "======================================="
echo "构建完成！现在可以分析Excel文件了！"
