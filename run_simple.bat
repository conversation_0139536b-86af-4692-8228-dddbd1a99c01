@echo off
chcp 65001 >nul

echo SOC智能研判分析工具 - Java简化版本
echo ==================================

echo 检查Java版本...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK 21.0.7
    pause
    exit /b 1
)

echo 编译Java源文件...
javac SOCAnalyzerSimple.java

if %errorlevel% equ 0 (
    echo 编译成功！
    echo 启动图形化界面...
    java SOCAnalyzerSimple
) else (
    echo 编译失败！
    pause
    exit /b 1
)

pause
