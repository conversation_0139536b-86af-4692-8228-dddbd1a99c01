#!/bin/bash

echo "SOC智能研判分析工具 - Java版本构建脚本"
echo "========================================"

# 检查Java版本
echo "检查Java版本..."
java -version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Java环境，请确保已安装JDK 21.0.7"
    exit 1
fi

# 检查Maven
echo "检查Maven..."
mvn -version
if [ $? -ne 0 ]; then
    echo "警告: 未找到Maven，尝试使用javac直接编译..."
    
    # 创建lib目录
    mkdir -p lib
    
    # 下载必要的JAR文件（如果不存在）
    if [ ! -f "lib/poi-5.2.5.jar" ]; then
        echo "下载Apache POI依赖..."
        curl -L "https://repo1.maven.org/maven2/org/apache/poi/poi/5.2.5/poi-5.2.5.jar" -o "lib/poi-5.2.5.jar"
        curl -L "https://repo1.maven.org/maven2/org/apache/poi/poi-ooxml/5.2.5/poi-ooxml-5.2.5.jar" -o "lib/poi-ooxml-5.2.5.jar"
        curl -L "https://repo1.maven.org/maven2/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar" -o "lib/poi-ooxml-schemas-4.1.2.jar"
        curl -L "https://repo1.maven.org/maven2/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar" -o "lib/commons-collections4-4.4.jar"
        curl -L "https://repo1.maven.org/maven2/org/apache/xmlbeans/xmlbeans/5.1.1/xmlbeans-5.1.1.jar" -o "lib/xmlbeans-5.1.1.jar"
        curl -L "https://repo1.maven.org/maven2/commons-codec/commons-codec/1.15/commons-codec-1.15.jar" -o "lib/commons-codec-1.15.jar"
        curl -L "https://repo1.maven.org/maven2/org/apache/commons/commons-compress/1.21/commons-compress-1.21.jar" -o "lib/commons-compress-1.21.jar"
    fi
    
    # 使用javac编译
    echo "编译Java源文件..."
    javac -cp "lib/*" SOCAnalyzer.java
    
    if [ $? -eq 0 ]; then
        echo "编译成功！"
        echo "运行程序: java -cp \".:lib/*\" SOCAnalyzer"
    else
        echo "编译失败！"
        exit 1
    fi
else
    # 使用Maven构建
    echo "使用Maven构建项目..."
    mvn clean compile package
    
    if [ $? -eq 0 ]; then
        echo "构建成功！"
        echo "可执行JAR文件: target/soc-analyzer-1.0.0.jar"
        echo "运行程序: java -jar target/soc-analyzer-1.0.0.jar"
    else
        echo "构建失败！"
        exit 1
    fi
fi

echo "========================================"
echo "构建完成！"
