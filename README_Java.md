# SOC智能研判分析工具 - Java图形化版本

## 概述

这是SOC智能研判分析工具的Java图形化版本，提供了友好的用户界面，支持选择本地Excel/CSV文件进行分析，并可以保存分析结果。

## 系统要求

- **JDK版本**: JDK 21.0.7 或更高版本
- **操作系统**: Windows, macOS, Linux
- **内存**: 建议512MB以上

## 文件说明

### 完整版本（需要Maven或手动下载依赖）
- `SOCAnalyzer.java` - 完整版本，支持Excel文件处理
- `pom.xml` - Maven项目配置文件
- `build.sh` / `build.bat` - 构建脚本

### 简化版本（无外部依赖）
- `SOCAnalyzerSimple.java` - 简化版本，支持CSV文件处理
- `run_simple.sh` / `run_simple.bat` - 直接运行脚本

## 快速开始

### 方法1: 使用简化版本（推荐）

1. **编译并运行**:
   ```bash
   # Linux/macOS
   chmod +x run_simple.sh
   ./run_simple.sh
   
   # Windows
   run_simple.bat
   ```

2. **手动编译运行**:
   ```bash
   javac SOCAnalyzerSimple.java
   java SOCAnalyzerSimple
   ```

### 方法2: 使用完整版本

1. **使用Maven构建**:
   ```bash
   # Linux/macOS
   chmod +x build.sh
   ./build.sh
   
   # Windows
   build.bat
   ```

2. **运行**:
   ```bash
   java -jar target/soc-analyzer-1.0.0.jar
   ```

## 使用说明

### 界面功能

1. **选择文件**: 点击"选择Excel文件"或"选择CSV文件"按钮
2. **开始分析**: 选择文件后，点击"开始分析"按钮
3. **查看结果**: 
   - 上半部分表格显示统计结果
   - 下半部分文本区域显示详细分析信息
4. **保存结果**: 点击"保存结果"按钮保存分析后的数据

### 输入文件要求

#### Excel版本 (SOCAnalyzer.java)
- 文件格式: `.xlsx`
- 必需列: `triageResult`, `triageRuleName`

#### CSV版本 (SOCAnalyzerSimple.java)
- 文件格式: `.csv`
- 编码: UTF-8
- 必需列: `triageResult`, `triageRuleName`

### 分析规则

程序会根据`triageRuleName`字段的内容判断分析类型：

- **AI分析**: 包含关键词（ai, 人工智能, 机器学习, 深度学习, 神经网络, 算法）
- **规则研判**: 其他所有情况

在`triageResult`字段后追加相应标签：`[ai分析]` 或 `[规则研判]`

## 功能特性

### 图形化界面
- 直观的文件选择对话框
- 实时显示分析进度
- 表格形式展示统计结果
- 详细的文本分析报告

### 数据处理
- 自动识别必需的列
- 智能判断分析类型
- 统计各种结论的分布
- 计算占比和汇总信息

### 结果保存
- 保存处理后的完整数据
- 支持自定义保存位置
- 保持原始数据结构

## 界面截图说明

程序界面包含以下部分：

```
┌─────────────────────────────────────────────────────────────┐
│ [选择文件] [开始分析] [保存结果] | 文件: xxx.xlsx            │
├─────────────────────────────────────────────────────────────┤
│ 统计结果表格                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 智能研判结论 | 分析类型 | 记录数量 | 占比(%)            │ │
│ │ 隐患:脆弱性[规则研判] | 规则研判 | 242 | 48.30         │ │
│ │ 有效告警:结果未知[ai分析] | AI分析 | 88 | 17.56        │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 详细分析结果                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ === 分析完成 ===                                       │ │
│ │ 文件: 1.xlsx                                           │ │
│ │ 总记录数: 501                                          │ │
│ │ === 智能研判结论统计 ===                               │ │
│ │ 隐患:脆弱性[规则研判] 共242条                          │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 状态: 就绪                                                  │
└─────────────────────────────────────────────────────────────┘
```

## 错误处理

程序包含完善的错误处理机制：

- 文件格式验证
- 必需列存在性检查
- 数据处理异常捕获
- 用户友好的错误提示

## 性能说明

- 支持大文件处理（建议不超过10万行）
- 后台线程处理，界面不会卡顿
- 内存使用优化

## 注意事项

1. **文件编码**: CSV文件请使用UTF-8编码
2. **列名匹配**: 确保文件包含`triageResult`和`triageRuleName`列
3. **Java版本**: 确保使用JDK 21.0.7或更高版本
4. **文件权限**: 确保对输入和输出文件有读写权限

## 故障排除

### 常见问题

1. **编译错误**:
   - 检查JDK版本是否为21.0.7+
   - 确保JAVA_HOME环境变量正确设置

2. **运行时错误**:
   - 检查文件格式是否正确
   - 确认必需的列是否存在

3. **界面显示问题**:
   - 尝试使用不同的Look and Feel
   - 检查系统字体设置

### 获取帮助

如遇到问题，请检查：
1. Java版本和环境变量
2. 输入文件格式和内容
3. 系统权限设置

## 版本信息

- **当前版本**: 1.0.0
- **兼容JDK**: 21.0.7+
- **支持格式**: Excel (.xlsx), CSV (.csv)
- **界面框架**: Java Swing
