#!/bin/bash

echo "SOC智能研判分析工具 - 构建JAR文件"
echo "================================"

# 检查Java版本
echo "检查Java版本..."
java -version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Java环境，请确保已安装JDK 21.0.7"
    exit 1
fi

# 创建构建目录
echo "创建构建目录..."
mkdir -p build
mkdir -p dist

# 编译Java文件
echo "编译Java源文件..."
javac -d build SOCAnalyzerSimple.java

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

# 创建MANIFEST.MF文件
echo "创建MANIFEST.MF文件..."
cat > build/MANIFEST.MF << EOF
Manifest-Version: 1.0
Main-Class: SOCAnalyzerSimple
Created-By: SOC Analysis Tool Builder

EOF

# 创建JAR文件
echo "创建JAR文件..."
cd build
jar cfm ../dist/soc-analyzer.jar MANIFEST.MF *.class
cd ..

if [ $? -eq 0 ]; then
    echo "构建成功！"
    echo "JAR文件位置: dist/soc-analyzer.jar"
    echo "运行命令: java -jar dist/soc-analyzer.jar"
    
    # 测试JAR文件
    echo "测试JAR文件..."
    java -jar dist/soc-analyzer.jar --version 2>/dev/null || echo "JAR文件创建成功，可以运行！"
else
    echo "创建JAR文件失败！"
    exit 1
fi

echo "================================"
echo "构建完成！"
