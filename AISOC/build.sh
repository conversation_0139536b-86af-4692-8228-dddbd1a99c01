#!/bin/bash

# AISOC智能研判分析工具构建脚本

echo "开始构建AISOC智能研判分析工具..."

# 检查Java版本
java -version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Java环境，请确保已安装Java 21或更高版本"
    exit 1
fi

# 检查Maven
mvn -version
if [ $? -ne 0 ]; then
    echo "错误: 未找到Maven，请确保已安装Maven"
    exit 1
fi

# 清理之前的构建
echo "清理之前的构建..."
mvn clean

# 编译和打包
echo "编译和打包..."
mvn package

if [ $? -eq 0 ]; then
    echo "构建成功！"
    echo "可执行JAR文件位置: target/soc-analyzer-gui-1.0.0.jar"
    echo ""
    echo "运行命令:"
    echo "java -jar target/soc-analyzer-gui-1.0.0.jar"
else
    echo "构建失败！"
    exit 1
fi
