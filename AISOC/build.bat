@echo off
chcp 65001 >nul

echo 开始构建AISOC智能研判分析工具...

REM 检查Java版本
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装Java 21或更高版本
    pause
    exit /b 1
)

REM 检查Maven
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven，请确保已安装Maven
    pause
    exit /b 1
)

REM 清理之前的构建
echo 清理之前的构建...
mvn clean

REM 编译和打包
echo 编译和打包...
mvn package

if %errorlevel% equ 0 (
    echo 构建成功！
    echo 可执行JAR文件位置: target\soc-analyzer-gui-1.0.0.jar
    echo.
    echo 运行命令:
    echo java -jar target\soc-analyzer-gui-1.0.0.jar
) else (
    echo 构建失败！
    pause
    exit /b 1
)

pause
