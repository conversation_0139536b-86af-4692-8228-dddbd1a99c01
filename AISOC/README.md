# AISOC智能研判分析工具

## 功能介绍

这是一个专门为AISOC设计的Java图形化分析工具，用于分析Excel文件中的智能研判数据。

### 主要功能

1. **文件选择功能** - 支持选择Excel (.xlsx) 文件
2. **一键分析按钮** - 点击即可开始分析
3. **三个结果显示区域**：
   - **AI研判结果** - 显示所有AI分析的结果
   - **规则研判结果** - 显示所有规则分析的结果
   - **总统计** - 显示整体统计信息

### 分析逻辑

工具会读取Excel文件中的以下字段：
- `triageResult` - 研判结果内容
- `triageRuleName` - 研判规则名称
- `domainId` 或 `组织` - 组织信息（可选字段）

分析规则：
- 如果 `triageRuleName` 字段包含 "AI"（不区分大小写），则归类为 **AI研判**
- 否则归类为 **规则研判**
- 如果 `domainId` 字段值为 "默认组织" 或 "上海"，则**不进入分析统计**，但会在结果中说明排除的记录数

输出格式：
- 结果前缀为 `triageResult` 字段的内容
- 后缀为 `[AI研判]` 或 `[规则研判]`
- 相同的结果只显示一次，后面显示出现次数（如：`疑似恶意文件[AI研判] (共3次)`）
- 如果只出现1次，则不显示次数

## 环境要求

- Java 8 或更高版本
- Maven 3.6 或更高版本

## 构建和运行

### 1. 构建项目

**Windows:**
```bash
build.bat
```

**Linux/Mac:**
```bash
chmod +x build.sh
./build.sh
```

### 2. 运行程序

**Windows:**
```bash
run.bat
```

**Linux/Mac:**
```bash
chmod +x run.sh
./run.sh
```

或者直接运行JAR文件：
```bash
java -jar target/soc-analyzer-gui-1.0.0.jar
```

## 使用说明

1. 启动程序后，点击 **"选择文件"** 按钮
2. 选择包含 `triageResult` 和 `triageRuleName` 字段的Excel文件
3. 点击 **"一键分析"** 按钮开始分析
4. 查看三个区域的分析结果：
   - 左侧：AI研判结果
   - 中间：规则研判结果
   - 右侧：总统计信息

## Excel文件格式要求

Excel文件必须包含以下列：
- `triageResult` - 智能研判结论（必需）
- `triageRuleName` - 智能研判策略名称（必需）
- `domainId` 或 `组织` - 组织信息（可选，用于过滤上海组织数据）

示例：
| triageResult | triageRuleName | domainId |
|-------------|----------------|----------|
| 疑似恶意文件 | AI_Detection_Rule | 北京 |
| 正常文件 | Standard_Rule | 上海 |
| 可疑网络连接 | AI_Network_Analysis | 深圳 |

## 注意事项

- 确保Excel文件格式正确，包含必需的列
- 程序会自动识别第一行为表头
- 支持中文文件名和内容
- 分析结果会实时显示在界面上

## 测试文件

项目中包含了三个测试Excel文件：
- `test_data.xlsx` - 基础测试数据
- `test_data_with_duplicates.xlsx` - 包含重复数据的测试文件，用于演示去重和计数功能
- `test_data_with_domain.xlsx` - 包含组织字段的测试文件，用于演示上海组织过滤功能

## 技术栈

- Java 8+
- Swing GUI
- Apache POI (Excel处理)
- Maven (构建工具)

## 项目结构

```
AISOC/
├── src/main/java/
│   └── SOCAnalyzerGUI.java    # 主程序文件
├── target/
│   └── soc-analyzer-gui-1.0.0.jar  # 可执行JAR文件
├── test_data.xlsx             # 测试Excel文件
├── pom.xml                    # Maven配置文件
├── build.sh / build.bat       # 构建脚本
├── run.sh / run.bat           # 运行脚本
└── README.md                  # 说明文档
```

## 故障排除

### 常见问题

1. **Java版本问题**
   - 确保安装了Java 8或更高版本
   - 运行 `java -version` 检查版本

2. **Maven构建失败**
   - 确保网络连接正常，Maven需要下载依赖
   - 尝试运行 `mvn clean` 清理后重新构建

3. **Excel文件格式问题**
   - 确保Excel文件包含 `triageResult` 和 `triageRuleName` 列
   - 支持 .xlsx 格式，不支持 .xls 格式

4. **程序无法启动**
   - 检查JAR文件是否存在于 target 目录
   - 确保有图形界面环境（不支持纯命令行环境）
