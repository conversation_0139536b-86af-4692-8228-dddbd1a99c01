@echo off
chcp 65001 >nul

set JAR_FILE=target\soc-analyzer-gui-1.0.0.jar

echo 启动AISOC智能研判分析工具...

REM 检查JAR文件是否存在
if not exist "%JAR_FILE%" (
    echo 错误: 未找到JAR文件 %JAR_FILE%
    echo 请先运行 build.bat 构建项目
    pause
    exit /b 1
)

REM 检查Java版本
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装Java 21或更高版本
    pause
    exit /b 1
)

REM 运行程序
echo 正在启动图形界面...
java -jar "%JAR_FILE%"

pause
