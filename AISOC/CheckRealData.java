import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import java.io.FileInputStream;

public class CheckRealData {
    public static void main(String[] args) {
        try (FileInputStream fis = new FileInputStream("/Users/<USER>/威胁告警_20250805143307.xlsx");
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            
            // 打印表头
            Row headerRow = sheet.getRow(0);
            System.out.println("=== 表头信息 ===");
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    System.out.println("列" + i + ": " + cell.getStringCellValue());
                }
            }
            
            System.out.println("\n总行数: " + (sheet.getLastRowNum() + 1));
            
            // 打印前几行数据
            System.out.println("\n=== 前5行数据内容 ===");
            for (int i = 1; i <= Math.min(5, sheet.getLastRowNum()); i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    System.out.println("\n行" + i + ":");
                    for (int j = 0; j < Math.min(headerRow.getLastCellNum(), 15); j++) { // 只显示前15列
                        Cell cell = row.getCell(j);
                        String value = getCellValueAsString(cell);
                        if (!value.isEmpty()) {
                            System.out.println("  " + headerRow.getCell(j).getStringCellValue() + ": " + value);
                        }
                    }
                }
            }
            
            // 查找关键字段
            System.out.println("\n=== 查找关键字段 ===");
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    String headerValue = cell.getStringCellValue().trim();
                    if (headerValue.contains("智能研判") || headerValue.contains("组织") || 
                        headerValue.contains("结论") || headerValue.contains("策略")) {
                        System.out.println("找到关键字段 - 列" + i + ": " + headerValue);
                    }
                }
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) return "";
        
        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue().toString();
                    } else {
                        return String.valueOf((long) cell.getNumericCellValue());
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                default:
                    return "";
            }
        } catch (Exception e) {
            return "";
        }
    }
}
