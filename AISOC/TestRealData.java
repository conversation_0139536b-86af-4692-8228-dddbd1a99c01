import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import java.io.FileInputStream;
import java.util.*;

public class TestRealData {
    public static void main(String[] args) {
        try (FileInputStream fis = new FileInputStream("/Users/<USER>/威胁告警_20250805143307.xlsx");
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            
            // 查找列索引
            int triageResultIndex = -1;  // 智能研判结论
            int triageRuleNameIndex = -1; // 智能研判策略
            int domainIdIndex = -1;       // 组织
            
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    String headerValue = cell.getStringCellValue().trim();
                    if ("triageResult".equalsIgnoreCase(headerValue) || "智能研判结论".equals(headerValue)) {
                        triageResultIndex = i;
                    } else if ("triageRuleName".equalsIgnoreCase(headerValue) || "智能研判策略".equals(headerValue)) {
                        triageRuleNameIndex = i;
                    } else if ("domainId".equalsIgnoreCase(headerValue) || "组织".equals(headerValue)) {
                        domainIdIndex = i;
                    }
                }
            }
            
            System.out.println("列索引 - 智能研判结论: " + triageResultIndex + 
                             ", 智能研判策略: " + triageRuleNameIndex + 
                             ", 组织: " + domainIdIndex);
            
            // 处理数据
            Map<String, Integer> aiResultsCount = new LinkedHashMap<>();
            Map<String, Integer> ruleResultsCount = new LinkedHashMap<>();
            int totalRecords = 0;
            int aiCount = 0;
            int ruleCount = 0;
            int shanghaiExcludedCount = 0;
            
            // 跳过表头行，从第2行开始（索引1）
            for (int i = 2; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                Cell triageResultCell = row.getCell(triageResultIndex);
                Cell triageRuleNameCell = row.getCell(triageRuleNameIndex);
                Cell domainIdCell = domainIdIndex >= 0 ? row.getCell(domainIdIndex) : null;
                
                if (triageResultCell == null) continue;
                
                String triageResult = getCellValueAsString(triageResultCell);
                String triageRuleName = getCellValueAsString(triageRuleNameCell);
                String domainId = getCellValueAsString(domainIdCell);
                
                if (triageResult == null || triageResult.trim().isEmpty()) continue;
                
                totalRecords++;
                
                // 检查是否包含上海
                if (isShanghaiOrganization(domainId)) {
                    shanghaiExcludedCount++;
                    if (shanghaiExcludedCount <= 5) { // 只显示前5个
                        System.out.println("跳过上海告警: " + triageResult + " (组织: " + domainId + ")");
                    }
                    continue;
                }
                
                // 判断是AI研判还是规则研判
                boolean isAI = isAIAnalysis(triageRuleName);
                String suffix = isAI ? "[AI研判]" : "[规则研判]";
                String result = triageResult + suffix;
                
                if (isAI) {
                    aiResultsCount.put(result, aiResultsCount.getOrDefault(result, 0) + 1);
                    aiCount++;
                } else {
                    ruleResultsCount.put(result, ruleResultsCount.getOrDefault(result, 0) + 1);
                    ruleCount++;
                }
            }
            
            // 输出结果
            System.out.println("\n=== 统计结果 ===");
            System.out.println("总记录数: " + totalRecords);
            System.out.println("上海告警跳过: " + shanghaiExcludedCount + " 条");
            System.out.println("有效分析记录: " + (totalRecords - shanghaiExcludedCount) + " 条");
            System.out.println("AI研判: " + aiCount + " 条");
            System.out.println("规则研判: " + ruleCount + " 条");
            
            System.out.println("\n=== AI研判结果 ===");
            if (aiResultsCount.isEmpty()) {
                System.out.println("无AI研判记录");
            } else {
                for (Map.Entry<String, Integer> entry : aiResultsCount.entrySet()) {
                    System.out.println(entry.getKey() + " (共" + entry.getValue() + "次)");
                }
            }
            
            System.out.println("\n=== 规则研判结果（前10种） ===");
            int count = 0;
            for (Map.Entry<String, Integer> entry : ruleResultsCount.entrySet()) {
                if (count >= 10) break;
                System.out.println(entry.getKey() + " (共" + entry.getValue() + "次)");
                count++;
            }
            
            if (ruleResultsCount.size() > 10) {
                System.out.println("... 还有 " + (ruleResultsCount.size() - 10) + " 种其他规则研判结果");
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) return "";
        
        try {
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue().toString();
                    } else {
                        return String.valueOf((long) cell.getNumericCellValue());
                    }
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                default:
                    return "";
            }
        } catch (Exception e) {
            return "";
        }
    }
    
    private static boolean isAIAnalysis(String triageRuleName) {
        if (triageRuleName == null || triageRuleName.trim().isEmpty()) {
            return false;
        }
        
        String trimmedRuleName = triageRuleName.trim();
        return "AI".equalsIgnoreCase(trimmedRuleName);
    }
    
    private static boolean isShanghaiOrganization(String domainId) {
        if (domainId == null || domainId.trim().isEmpty()) {
            return false;
        }
        
        String trimmedDomainId = domainId.trim();
        return trimmedDomainId.contains("上海");
    }
}
