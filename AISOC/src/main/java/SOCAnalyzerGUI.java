import javax.swing.*;
import javax.swing.filechooser.FileNameExtensionFilter;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.io.*;
import java.util.*;
import java.util.List;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

/**
 * SOC智能研判分析工具 - AISOC版本
 * 支持Excel文件分析，按照triageResult和triageRuleName字段进行分析
 */
public class SOCAnalyzerGUI extends JFrame {
    private JButton selectFileButton;
    private JButton analyzeButton;
    private JLabel filePathLabel;
    
    // 三个结果显示区域
    private JTextArea aiResultArea;
    private JTextArea ruleResultArea;
    private JTextArea statisticsArea;
    
    private File selectedFile;
    private List<Map<String, String>> analyzedData;
    
    public SOCAnalyzerGUI() {
        initializeGUI();
    }
    
    private void initializeGUI() {
        setTitle("SOC智能研判分析工具 - AISOC版本");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setSize(1200, 800);
        setLocationRelativeTo(null);
        
        // 创建主面板
        JPanel mainPanel = new JPanel(new BorderLayout());
        
        // 顶部面板 - 文件选择和操作按钮
        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        selectFileButton = new JButton("选择文件");
        analyzeButton = new JButton("一键分析");
        filePathLabel = new JLabel("未选择文件 (支持Excel格式)");
        
        analyzeButton.setEnabled(false);
        
        topPanel.add(selectFileButton);
        topPanel.add(analyzeButton);
        topPanel.add(new JLabel("  |  "));
        topPanel.add(filePathLabel);
        
        // 中间面板 - 三个结果显示区域
        JPanel centerPanel = new JPanel(new GridLayout(1, 3, 10, 10));
        centerPanel.setBorder(BorderFactory.createEmptyBorder(10, 10, 10, 10));
        
        // AI研判结果区域
        aiResultArea = new JTextArea();
        aiResultArea.setEditable(false);
        aiResultArea.setFont(new java.awt.Font(java.awt.Font.MONOSPACED, java.awt.Font.PLAIN, 12));
        JScrollPane aiScrollPane = new JScrollPane(aiResultArea);
        aiScrollPane.setBorder(BorderFactory.createTitledBorder("AI研判结果"));

        // 规则研判结果区域
        ruleResultArea = new JTextArea();
        ruleResultArea.setEditable(false);
        ruleResultArea.setFont(new java.awt.Font(java.awt.Font.MONOSPACED, java.awt.Font.PLAIN, 12));
        JScrollPane ruleScrollPane = new JScrollPane(ruleResultArea);
        ruleScrollPane.setBorder(BorderFactory.createTitledBorder("规则研判结果"));

        // 总统计区域
        statisticsArea = new JTextArea();
        statisticsArea.setEditable(false);
        statisticsArea.setFont(new java.awt.Font(java.awt.Font.MONOSPACED, java.awt.Font.PLAIN, 12));
        JScrollPane statsScrollPane = new JScrollPane(statisticsArea);
        statsScrollPane.setBorder(BorderFactory.createTitledBorder("总统计"));
        
        centerPanel.add(aiScrollPane);
        centerPanel.add(ruleScrollPane);
        centerPanel.add(statsScrollPane);
        
        // 底部面板 - 状态栏
        JPanel bottomPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        JLabel statusLabel = new JLabel("就绪 - 请选择Excel文件进行分析");
        bottomPanel.add(statusLabel);
        
        // 添加组件到主面板
        mainPanel.add(topPanel, BorderLayout.NORTH);
        mainPanel.add(centerPanel, BorderLayout.CENTER);
        mainPanel.add(bottomPanel, BorderLayout.SOUTH);
        
        add(mainPanel);
        
        // 添加事件监听器
        selectFileButton.addActionListener(new SelectFileListener());
        analyzeButton.addActionListener(new AnalyzeListener());
    }
    
    // 文件选择监听器
    private class SelectFileListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            JFileChooser fileChooser = new JFileChooser();
            fileChooser.setFileFilter(new FileNameExtensionFilter("Excel文件 (*.xlsx)", "xlsx"));
            
            int result = fileChooser.showOpenDialog(SOCAnalyzerGUI.this);
            if (result == JFileChooser.APPROVE_OPTION) {
                selectedFile = fileChooser.getSelectedFile();
                filePathLabel.setText(selectedFile.getName());
                analyzeButton.setEnabled(true);
                
                // 清空之前的结果
                aiResultArea.setText("");
                ruleResultArea.setText("");
                statisticsArea.setText("");
            }
        }
    }
    
    // 分析监听器
    private class AnalyzeListener implements ActionListener {
        @Override
        public void actionPerformed(ActionEvent e) {
            if (selectedFile == null) {
                JOptionPane.showMessageDialog(SOCAnalyzerGUI.this, "请先选择Excel文件", "错误", JOptionPane.ERROR_MESSAGE);
                return;
            }
            
            // 在后台线程中执行分析
            SwingWorker<Void, String> worker = new SwingWorker<Void, String>() {
                @Override
                protected Void doInBackground() throws Exception {
                    analyzeExcelFile();
                    return null;
                }
                
                @Override
                protected void done() {
                    try {
                        get(); // 检查是否有异常
                        JOptionPane.showMessageDialog(SOCAnalyzerGUI.this, "分析完成！", "成功", JOptionPane.INFORMATION_MESSAGE);
                    } catch (Exception ex) {
                        JOptionPane.showMessageDialog(SOCAnalyzerGUI.this, 
                            "分析过程中出现错误: " + ex.getMessage(), 
                            "错误", JOptionPane.ERROR_MESSAGE);
                        ex.printStackTrace();
                    }
                }
            };
            
            worker.execute();
        }
    }
    
    // 分析Excel文件的核心方法
    private void analyzeExcelFile() throws Exception {
        analyzedData = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(selectedFile);
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            
            if (headerRow == null) {
                throw new Exception("Excel文件为空或格式不正确");
            }
            
            // 查找列索引
            int triageResultIndex = -1;
            int triageRuleNameIndex = -1;
            int domainIdIndex = -1;

            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    String headerValue = cell.getStringCellValue().trim();
                    if ("triageResult".equalsIgnoreCase(headerValue)) {
                        triageResultIndex = i;
                    } else if ("triageRuleName".equalsIgnoreCase(headerValue)) {
                        triageRuleNameIndex = i;
                    } else if ("domainId".equalsIgnoreCase(headerValue) || "组织".equals(headerValue)) {
                        domainIdIndex = i;
                    }
                }
            }

            if (triageResultIndex == -1 || triageRuleNameIndex == -1) {
                throw new Exception("未找到必需的列: triageResult 或 triageRuleName");
            }
            
            // 处理数据行 - 使用Map统计相同结果的次数
            Map<String, Integer> aiResultsCount = new LinkedHashMap<>();
            Map<String, Integer> ruleResultsCount = new LinkedHashMap<>();
            int totalRecords = 0;
            int aiCount = 0;
            int ruleCount = 0;
            int shanghaiExcludedCount = 0; // 上海组织被排除的记录数

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                Cell triageResultCell = row.getCell(triageResultIndex);
                Cell triageRuleNameCell = row.getCell(triageRuleNameIndex);
                Cell domainIdCell = domainIdIndex >= 0 ? row.getCell(domainIdIndex) : null;

                if (triageResultCell == null) continue;

                String triageResult = getCellValueAsString(triageResultCell);
                String triageRuleName = getCellValueAsString(triageRuleNameCell);
                String domainId = getCellValueAsString(domainIdCell);

                if (triageResult == null || triageResult.trim().isEmpty()) continue;

                totalRecords++;

                // 检查是否为上海组织，如果是则排除但计数
                if (isShanghaiOrganization(domainId)) {
                    shanghaiExcludedCount++;
                    continue; // 不进入分析统计
                }

                // 判断是AI研判还是规则研判
                boolean isAI = isAIAnalysis(triageRuleName);
                String suffix = isAI ? "[AI研判]" : "[规则研判]";
                String result = triageResult + suffix;

                if (isAI) {
                    aiResultsCount.put(result, aiResultsCount.getOrDefault(result, 0) + 1);
                    aiCount++;
                } else {
                    ruleResultsCount.put(result, ruleResultsCount.getOrDefault(result, 0) + 1);
                    ruleCount++;
                }
            }
            
            // 更新GUI显示
            final int finalTotalRecords = totalRecords;
            final int finalAiCount = aiCount;
            final int finalRuleCount = ruleCount;
            final int finalShanghaiExcludedCount = shanghaiExcludedCount;
            SwingUtilities.invokeLater(new Runnable() {
                @Override
                public void run() {
                    updateResultAreas(aiResultsCount, ruleResultsCount, finalTotalRecords, finalAiCount, finalRuleCount, finalShanghaiExcludedCount);
                }
            });
        }
    }
    
    // 获取单元格值作为字符串
    private String getCellValueAsString(Cell cell) {
        if (cell == null) return "";
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
    
    // 判断是否为AI分析
    private boolean isAIAnalysis(String triageRuleName) {
        if (triageRuleName == null || triageRuleName.trim().isEmpty()) {
            return false;
        }

        String lowerRuleName = triageRuleName.toLowerCase();
        return lowerRuleName.contains("ai");
    }

    // 判断是否为上海组织（需要排除的组织）
    private boolean isShanghaiOrganization(String domainId) {
        if (domainId == null || domainId.trim().isEmpty()) {
            return false;
        }

        String trimmedDomainId = domainId.trim();
        return "默认组织".equals(trimmedDomainId) || "上海".equals(trimmedDomainId);
    }
    
    // 更新结果显示区域
    private void updateResultAreas(Map<String, Integer> aiResultsCount, Map<String, Integer> ruleResultsCount,
                                   int totalRecords, int aiCount, int ruleCount, int shanghaiExcludedCount) {
        // 更新AI研判结果
        StringBuilder aiText = new StringBuilder();
        aiText.append("=== AI研判结果 ===\n");
        aiText.append("共 ").append(aiCount).append(" 条记录\n\n");
        for (Map.Entry<String, Integer> entry : aiResultsCount.entrySet()) {
            String result = entry.getKey();
            int count = entry.getValue();
            if (count == 1) {
                aiText.append(result).append("\n");
            } else {
                aiText.append(result).append(" (共").append(count).append("次)\n");
            }
        }
        aiResultArea.setText(aiText.toString());

        // 更新规则研判结果
        StringBuilder ruleText = new StringBuilder();
        ruleText.append("=== 规则研判结果 ===\n");
        ruleText.append("共 ").append(ruleCount).append(" 条记录\n\n");
        for (Map.Entry<String, Integer> entry : ruleResultsCount.entrySet()) {
            String result = entry.getKey();
            int count = entry.getValue();
            if (count == 1) {
                ruleText.append(result).append("\n");
            } else {
                ruleText.append(result).append(" (共").append(count).append("次)\n");
            }
        }
        ruleResultArea.setText(ruleText.toString());

        // 更新总统计
        StringBuilder statsText = new StringBuilder();
        statsText.append("=== 总统计 ===\n");
        statsText.append("文件: ").append(selectedFile.getName()).append("\n");
        statsText.append("总记录数: ").append(totalRecords).append("\n");

        // 计算有效分析记录数（排除上海组织）
        int effectiveRecords = totalRecords - shanghaiExcludedCount;
        statsText.append("有效分析记录: ").append(effectiveRecords).append(" 条\n");

        if (shanghaiExcludedCount > 0) {
            statsText.append("上海组织记录: ").append(shanghaiExcludedCount)
                     .append(" 条 [不进入AI误报率统计分析]\n");
        }

        statsText.append("\n=== 分析结果统计 ===\n");
        if (effectiveRecords > 0) {
            statsText.append("AI研判: ").append(aiCount).append(" 条 (")
                     .append(String.format("%.2f", (double) aiCount / effectiveRecords * 100))
                     .append("%)\n");
            statsText.append("规则研判: ").append(ruleCount).append(" 条 (")
                     .append(String.format("%.2f", (double) ruleCount / effectiveRecords * 100))
                     .append("%)\n");
        } else {
            statsText.append("AI研判: ").append(aiCount).append(" 条\n");
            statsText.append("规则研判: ").append(ruleCount).append(" 条\n");
        }

        statsText.append("不同结果类型: AI研判").append(aiResultsCount.size())
                 .append("种，规则研判").append(ruleResultsCount.size()).append("种\n");
        statisticsArea.setText(statsText.toString());
    }
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(new Runnable() {
            @Override
            public void run() {
                new SOCAnalyzerGUI().setVisible(true);
            }
        });
    }
}
