# AISOC智能研判分析工具 - 使用说明

## 快速开始

### 1. 运行程序
双击运行脚本：
- Windows: `run.bat`
- Linux/Mac: `run.sh`

或者使用命令行：
```bash
java -jar target/soc-analyzer-gui-1.0.0.jar
```

### 2. 使用步骤
1. 点击"选择文件"按钮，选择Excel文件（.xlsx格式）
2. 点击"一键分析"按钮开始分析
3. 查看三个区域的分析结果

## 界面说明

### 三个结果显示区域

1. **左侧 - AI研判结果**
   - 显示所有triageRuleName包含"AI"的记录
   - 格式：`原始结果[AI研判]`
   - 相同结果会合并显示，并显示出现次数

2. **中间 - 规则研判结果**
   - 显示所有triageRuleName不包含"AI"的记录
   - 格式：`原始结果[规则研判]`
   - 相同结果会合并显示，并显示出现次数

3. **右侧 - 总统计**
   - 显示文件名、总记录数
   - 显示AI研判和规则研判的数量和百分比
   - 显示不同结果类型的数量

## 输出示例

### AI研判结果示例：
```
=== AI研判结果 ===
共 12 条记录

疑似恶意文件[AI研判] (共3次)
可疑网络连接[AI研判] (共2次)
恶意代码检测[AI研判] (共4次)
异常行为检测[AI研判]
威胁情报匹配[AI研判]
高风险文件[AI研判]
智能分析结果[AI研判]
```

### 规则研判结果示例：
```
=== 规则研判结果 ===
共 13 条记录

正常文件[规则研判] (共2次)
安全文件[规则研判]
正常网络流量[规则研判]
合规文件[规则研判] (共2次)
正常操作[规则研判] (共3次)
低风险文件[规则研判] (共2次)
基础检查通过[规则研判]
```

## Excel文件要求

### 必需列
- `triageResult`: 智能研判结论
- `triageRuleName`: 智能研判策略名称

### 示例数据格式
| triageResult | triageRuleName |
|-------------|----------------|
| 疑似恶意文件 | AI_Detection_Rule |
| 正常文件 | Standard_Rule |
| 可疑网络连接 | AI_Network_Analysis |

## 分析规则

- 如果`triageRuleName`包含"AI"（不区分大小写），归类为**AI研判**
- 否则归类为**规则研判**
- 相同的结果只显示一次，后面显示出现次数
- 只出现1次的结果不显示次数

## 测试文件

项目提供了两个测试文件：
1. `test_data.xlsx` - 基础测试数据
2. `test_data_with_duplicates.xlsx` - 包含重复数据，演示去重功能

## 注意事项

1. 只支持.xlsx格式的Excel文件
2. 确保Excel文件包含必需的列名
3. 程序会自动识别第一行为表头
4. 支持中文文件名和内容
5. 需要图形界面环境运行
