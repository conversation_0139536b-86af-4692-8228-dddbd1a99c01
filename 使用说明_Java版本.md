# SOC智能研判分析工具 - Java图形化版本使用说明

## 🎯 快速开始

### 1. 构建JAR文件

```bash
# Linux/macOS
chmod +x build_jar.sh
./build_jar.sh

# Windows
build_jar.bat
```

### 2. 启动图形化界面

```bash
# 方法1: 直接运行JAR文件
java -jar dist/soc-analyzer.jar

# 方法2: 使用启动脚本
# Linux/macOS
chmod +x start_gui.sh
./start_gui.sh

# Windows
start_gui.bat
```

## 📋 系统要求

- **Java版本**: JDK 8 或更高版本
- **操作系统**: Windows, macOS, Linux
- **内存**: 建议512MB以上
- **文件格式**: CSV文件（UTF-8编码）

## 🖥️ 界面说明

程序启动后会显示图形化界面，包含以下部分：

### 顶部操作栏
- **选择CSV文件**: 选择要分析的CSV文件
- **开始分析**: 对选中的文件进行分析
- **保存结果**: 将分析结果保存到新文件

### 中间统计表格
显示分析结果的统计信息：
- 智能研判结论
- 分析类型（AI分析/规则研判）
- 记录数量
- 占比百分比

### 底部详细结果
显示详细的分析过程和结果文本

## 📁 文件要求

### 输入文件格式
- **文件类型**: CSV文件 (*.csv)
- **编码格式**: UTF-8
- **必需列**: 
  - `triageResult` - 智能研判结论
  - `triageRuleName` - 智能研判策略

### 示例CSV格式
```csv
triageResult,triageRuleName,其他列...
隐患:脆弱性,预置-疑似隐患类告警,其他数据...
有效告警:攻击失败,AI,其他数据...
```

## 🔍 分析规则

程序会根据`triageRuleName`字段判断分析类型：

### AI分析 `[ai分析]`
当策略名称包含以下关键词时：
- ai
- 人工智能
- 机器学习
- 深度学习
- 神经网络
- 算法

### 规则研判 `[规则研判]`
其他所有情况（包括空值）

## 📊 输出结果

### 处理后的数据
- 在`triageResult`字段后追加分析类型标签
- 例如：`隐患:脆弱性` → `隐患:脆弱性[规则研判]`

### 统计信息
- 各种结论类型的数量统计
- AI分析 vs 规则研判的比例
- 详细的分析报告

## 🛠️ 使用步骤

1. **启动程序**
   ```bash
   java -jar dist/soc-analyzer.jar
   ```

2. **选择文件**
   - 点击"选择CSV文件"按钮
   - 选择包含`triageResult`和`triageRuleName`列的CSV文件

3. **开始分析**
   - 点击"开始分析"按钮
   - 程序会在后台处理数据，界面显示进度

4. **查看结果**
   - 上方表格显示统计结果
   - 下方文本区域显示详细信息

5. **保存结果**
   - 点击"保存结果"按钮
   - 选择保存位置，程序会生成处理后的CSV文件

## 📈 示例分析结果

```
=== 分析完成 ===
文件: 1.csv
总记录数: 501
处理记录数: 501

=== 智能研判结论统计 ===
隐患:脆弱性[规则研判] 共242条
有效告警:结果未知[ai分析] 共88条
无效告警:业务触发[规则研判] 共55条
有效告警:攻击成功[规则研判] 共24条
需人工研判[规则研判] 共24条
...

=== 汇总统计 ===
AI分析: 155条记录
规则研判: 346条记录
结论种类: 11种
```

## ⚠️ 注意事项

1. **文件编码**: 确保CSV文件使用UTF-8编码
2. **列名匹配**: 文件必须包含`triageResult`和`triageRuleName`列
3. **文件大小**: 建议单个文件不超过10万行数据
4. **Java版本**: 确保安装了Java 8或更高版本

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Java是否正确安装：`java -version`
   - 确保JAR文件存在：`ls -la dist/soc-analyzer.jar`

2. **文件无法打开**
   - 检查文件格式是否为CSV
   - 确认文件编码为UTF-8
   - 验证必需的列是否存在

3. **分析结果异常**
   - 检查数据格式是否正确
   - 确认列名拼写无误

### 重新构建

如果遇到问题，可以重新构建JAR文件：

```bash
# 清理构建目录
rm -rf build dist

# 重新构建
./build_jar.sh
```

## 📝 文件结构

```
soc分析/
├── SOCAnalyzerSimple.java    # 主程序源码
├── build_jar.sh             # 构建脚本(Linux/macOS)
├── build_jar.bat            # 构建脚本(Windows)
├── start_gui.sh             # 启动脚本(Linux/macOS)
├── start_gui.bat            # 启动脚本(Windows)
├── build/                   # 编译输出目录
│   ├── *.class             # 编译后的类文件
│   └── MANIFEST.MF         # JAR清单文件
└── dist/                   # 发布目录
    └── soc-analyzer.jar    # 可执行JAR文件
```

## 🎉 完成！

现在您可以使用以下命令启动图形化的SOC分析工具：

```bash
java -jar dist/soc-analyzer.jar
```

程序将打开一个友好的图形界面，支持：
- ✅ 选择本地CSV文件
- ✅ 实时分析进度显示
- ✅ 统计结果表格展示
- ✅ 详细分析报告
- ✅ 保存处理后的结果文件

享受使用吧！ 🚀
