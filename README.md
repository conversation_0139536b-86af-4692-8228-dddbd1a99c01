# SOC智能研判分析工具

## 功能描述

这是一个用于SOC（安全运营中心）智能研判分析的Python程序。程序能够自动分析Excel文件中的"智能研判结论"和"智能研判策略"字段，并根据策略类型在结论后追加相应的标签。

## 功能特点

- 自动识别Excel文件中的"智能研判结论"和"智能研判策略"列
- 根据智能研判策略判断分析类型：
  - 如果策略包含AI相关关键词（ai、人工智能、机器学习、深度学习、神经网络、算法），则追加`[ai分析]`
  - 否则追加`[规则研判]`
- 保留原始内容，只在结论后追加标签
- 生成新的分析结果文件，不覆盖原文件

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install pandas openpyxl
```

## 使用方法

```bash
python soc.py -f xxx.xlsx
```

### 参数说明

- `-f` 或 `--file`: 指定要分析的Excel文件路径（必需）

### 使用示例

```bash
# 分析示例文件
python soc.py -f sample_data.xlsx

# 分析其他Excel文件
python soc.py -f /path/to/your/data.xlsx
```

## 输入文件要求

Excel文件必须包含以下列：
- **智能研判结论**: 包含研判结论的文本内容
- **智能研判策略**: 包含策略描述的文本内容

列名可以是包含这些关键词的任何名称，程序会自动识别。

## 输出结果

程序会生成一个新的Excel文件，文件名为原文件名加上`_analyzed`后缀。例如：
- 输入文件：`data.xlsx`
- 输出文件：`data_analyzed.xlsx`

## 分析规则

程序根据"智能研判策略"字段的内容判断分析类型：

### AI分析标签 `[ai分析]`
当策略中包含以下关键词时：
- ai
- 人工智能
- 机器学习
- 深度学习
- 神经网络
- 算法

### 规则研判标签 `[规则研判]`
当策略中不包含上述AI相关关键词时，或策略为空时。

## 创建测试数据

运行以下命令创建示例测试文件：

```bash
python create_sample.py
```

这将创建一个名为`sample_data.xlsx`的示例文件，包含测试数据。

## 示例输出

处理前的智能研判结论：
```
检测到异常登录行为，建议进一步调查
```

处理后的智能研判结论：
```
检测到异常登录行为，建议进一步调查[ai分析]
```

## 错误处理

程序包含完善的错误处理机制：
- 文件不存在检查
- Excel文件格式验证
- 必需列存在性检查
- 数据处理异常捕获

## 注意事项

1. 确保Excel文件包含"智能研判结论"和"智能研判策略"列
2. 程序不会修改原始文件，而是创建新的分析结果文件
3. 如果结论已经包含`[ai分析]`或`[规则研判]`标签，程序不会重复添加
4. 支持.xlsx和.xls格式的Excel文件
