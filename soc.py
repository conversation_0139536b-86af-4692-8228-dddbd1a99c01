#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOC分析程序
使用方法: python soc.py -f xxx.xlsx
功能: 自动分析Excel文件中的triageResult和triageRuleName字段
"""

import argparse
import pandas as pd
import sys
import os


class SOCAnalyzer:
    """SOC分析器类"""
    
    def __init__(self, file_path):
        """
        初始化SOC分析器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.df = None
        
    def load_excel(self):
        """
        加载Excel文件
        
        Returns:
            bool: 加载成功返回True，失败返回False
        """
        try:
            # 尝试读取Excel文件
            self.df = pd.read_excel(self.file_path)
            print(f"成功加载文件: {self.file_path}")
            print(f"数据行数: {len(self.df)}")
            print(f"数据列数: {len(self.df.columns)}")
            return True
        except FileNotFoundError:
            print(f"错误: 文件 '{self.file_path}' 不存在")
            return False
        except Exception as e:
            print(f"错误: 无法读取文件 '{self.file_path}': {str(e)}")
            return False
    
    def find_columns(self):
        """
        获取指定的列名

        Returns:
            tuple: (智能研判结论列名, 智能研判策略列名)
        """
        conclusion_col = "triageResult"
        strategy_col = "triageRuleName"

        # 检查列是否存在
        if conclusion_col not in self.df.columns:
            conclusion_col = None

        if strategy_col not in self.df.columns:
            strategy_col = None

        return conclusion_col, strategy_col
    
    def determine_analysis_type(self, strategy_value):
        """
        根据智能研判策略确定分析类型
        
        Args:
            strategy_value: 智能研判策略的值

        Returns:
            str: "[ai分析]" 或 "[规则研判]"
        """
        if pd.isna(strategy_value):
            return "[规则研判]"
        
        strategy_str = str(strategy_value).lower()
        
        # 如果策略中包含"ai"相关关键词，则为AI分析
        ai_keywords = ["ai", "人工智能", "机器学习", "深度学习", "神经网络", "算法"]
        
        for keyword in ai_keywords:
            if keyword in strategy_str:
                return "[ai分析]"
        
        return "[规则研判]"
    
    def process_data(self):
        """
        处理数据，在智能研判结论后追加分析类型标签
        
        Returns:
            bool: 处理成功返回True，失败返回False
        """
        conclusion_col, strategy_col = self.find_columns()
        
        if conclusion_col is None:
            print("错误: 未找到'triageResult'列")
            print(f"可用列名: {list(self.df.columns)}")
            return False

        if strategy_col is None:
            print("错误: 未找到'triageRuleName'列")
            print(f"可用列名: {list(self.df.columns)}")
            return False

        print(f"找到智能研判结论列: {conclusion_col}")
        print(f"找到智能研判策略列: {strategy_col}")
        
        # 处理每一行数据
        processed_count = 0
        for index, row in self.df.iterrows():
            conclusion = row[conclusion_col]
            strategy = row[strategy_col]
            
            # 跳过空值
            if pd.isna(conclusion):
                continue
            
            # 确定分析类型
            analysis_type = self.determine_analysis_type(strategy)
            
            # 在结论后追加分析类型（如果还没有追加过）
            conclusion_str = str(conclusion)
            if not (conclusion_str.endswith("[ai分析]") or conclusion_str.endswith("[规则研判]")):
                new_conclusion = conclusion_str + analysis_type
                self.df.at[index, conclusion_col] = new_conclusion
                processed_count += 1
        
        print(f"成功处理 {processed_count} 条记录")

        # 生成统计报告
        self.generate_statistics_report()

        return True

    def generate_statistics_report(self):
        """生成智能研判结论统计报告表格"""
        try:
            conclusion_col, strategy_col = self.find_columns()

            print("正在生成统计报告...")

            # 统计智能研判结论的分布
            conclusion_stats = {}

            for index, row in self.df.iterrows():
                conclusion = str(row[conclusion_col])

                # 提取原始结论内容（去掉标签）
                if '[ai分析]' in conclusion:
                    original_conclusion = conclusion.replace('[ai分析]', '')
                    analysis_type = 'ai分析'
                elif '[规则研判]' in conclusion:
                    original_conclusion = conclusion.replace('[规则研判]', '')
                    analysis_type = '规则研判'
                else:
                    original_conclusion = conclusion
                    analysis_type = '未知'

                # 生成完整的结论标签
                full_conclusion = f"{original_conclusion}[{analysis_type}]"

                if full_conclusion not in conclusion_stats:
                    conclusion_stats[full_conclusion] = 0
                conclusion_stats[full_conclusion] += 1

            # 创建统计数据列表
            stats_data = []
            ai_total = 0
            rule_total = 0

            for conclusion, count in conclusion_stats.items():
                if '[ai分析]' in conclusion:
                    analysis_type = 'AI分析'
                    ai_total += count
                elif '[规则研判]' in conclusion:
                    analysis_type = '规则研判'
                    rule_total += count
                else:
                    analysis_type = '未知'

                stats_data.append({
                    '智能研判结论': conclusion,
                    '分析类型': analysis_type,
                    '记录数量': count,
                    '占比(%)': round(count / len(self.df) * 100, 2)
                })

            # 创建统计DataFrame
            stats_df = pd.DataFrame(stats_data)

            # 按记录数量排序
            stats_df = stats_df.sort_values('记录数量', ascending=False)

            # 生成输出文件名
            base_name = os.path.splitext(self.file_path)[0]
            stats_file = f"{base_name}_statistics.xlsx"

            # 使用ExcelWriter创建多个工作表
            with pd.ExcelWriter(stats_file, engine='openpyxl') as writer:
                # 详细统计表
                stats_df.to_excel(writer, sheet_name='智能研判结论统计', index=False)

                # 汇总统计表
                summary_data = [
                    {'分析类型': 'AI分析', '结论种类': len(stats_df[stats_df['分析类型'] == 'AI分析']), '记录数量': ai_total, '占比(%)': round(ai_total / len(self.df) * 100, 2)},
                    {'分析类型': '规则研判', '结论种类': len(stats_df[stats_df['分析类型'] == '规则研判']), '记录数量': rule_total, '占比(%)': round(rule_total / len(self.df) * 100, 2)},
                    {'分析类型': '总计', '结论种类': len(stats_df), '记录数量': len(self.df), '占比(%)': 100.0}
                ]
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)

                # AI分析详细列表
                ai_stats = stats_df[stats_df['分析类型'] == 'AI分析'].copy()
                if not ai_stats.empty:
                    ai_stats.to_excel(writer, sheet_name='AI分析结论', index=False)

                # 规则研判详细列表
                rule_stats = stats_df[stats_df['分析类型'] == '规则研判'].copy()
                if not rule_stats.empty:
                    rule_stats.to_excel(writer, sheet_name='规则研判结论', index=False)

            print(f"统计报告已保存到: {stats_file}")

            # 在控制台显示详细信息
            print("\n=== 智能研判结论统计 ===")
            for index, row in stats_df.iterrows():
                print(f"{row['智能研判结论']} 共{row['记录数量']}条")

            print(f"\n=== 汇总统计 ===")
            print(f"总记录数: {len(self.df)}")
            print(f"结论种类: {len(stats_df)}")
            print(f"AI分析: {len(stats_df[stats_df['分析类型'] == 'AI分析'])}种结论, {ai_total}条记录")
            print(f"规则研判: {len(stats_df[stats_df['分析类型'] == '规则研判'])}种结论, {rule_total}条记录")

        except Exception as e:
            print(f"生成统计报告时出错: {str(e)}")

    def save_result(self):
        """
        保存处理结果到新文件
        
        Returns:
            bool: 保存成功返回True，失败返回False
        """
        try:
            # 生成输出文件名
            base_name = os.path.splitext(self.file_path)[0]
            output_file = f"{base_name}_analyzed.xlsx"

            # 保存到新文件
            self.df.to_excel(output_file, index=False)
            print(f"分析结果已保存到: {output_file}")
            return True
        except Exception as e:
            print(f"错误: 无法保存文件: {str(e)}")
            return False
    
    def analyze(self):
        """
        执行完整的分析流程
        
        Returns:
            bool: 分析成功返回True，失败返回False
        """
        if not self.load_excel():
            return False
        
        if not self.process_data():
            return False
        
        if not self.save_result():
            return False
        
        return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="SOC智能研判分析工具")
    parser.add_argument("-f", "--file", required=True, help="要分析的Excel文件路径")
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.file):
        print(f"错误: 文件 '{args.file}' 不存在")
        sys.exit(1)

    # 检查文件扩展名
    if not args.file.lower().endswith(('.xlsx', '.xls')):
        print(f"错误: 文件 '{args.file}' 不是Excel文件")
        sys.exit(1)
    
    # 创建分析器并执行分析
    analyzer = SOCAnalyzer(args.file)
    
    print("开始SOC智能研判分析...")
    print("=" * 50)
    
    if analyzer.analyze():
        print("=" * 50)
        print("分析完成!")
    else:
        print("=" * 50)
        print("分析失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
