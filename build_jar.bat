@echo off
chcp 65001 >nul

echo SOC智能研判分析工具 - 构建JAR文件
echo ================================

echo 检查Java版本...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK 21.0.7
    pause
    exit /b 1
)

echo 创建构建目录...
if not exist build mkdir build
if not exist dist mkdir dist

echo 编译Java源文件...
javac -d build SOCAnalyzerSimple.java

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 创建MANIFEST.MF文件...
echo Manifest-Version: 1.0 > build\MANIFEST.MF
echo Main-Class: SOCAnalyzerSimple >> build\MANIFEST.MF
echo Created-By: SOC Analysis Tool Builder >> build\MANIFEST.MF
echo. >> build\MANIFEST.MF

echo 创建JAR文件...
cd build
jar cfm ..\dist\soc-analyzer.jar MANIFEST.MF *.class
cd ..

if %errorlevel% equ 0 (
    echo 构建成功！
    echo JAR文件位置: dist\soc-analyzer.jar
    echo 运行命令: java -jar dist\soc-analyzer.jar
) else (
    echo 创建JAR文件失败！
    pause
    exit /b 1
)

echo ================================
echo 构建完成！
pause
