@echo off
chcp 65001 >nul

echo SOC智能研判分析工具 - Java版本构建脚本
echo ========================================

echo 检查Java版本...
java -version
if %errorlevel% neq 0 (
    echo 错误: 未找到Java环境，请确保已安装JDK 21.0.7
    pause
    exit /b 1
)

echo 检查Maven...
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 警告: 未找到Maven，尝试使用javac直接编译...
    
    if not exist lib mkdir lib
    
    echo 使用javac编译...
    javac -cp "lib/*" SOCAnalyzer.java
    
    if %errorlevel% equ 0 (
        echo 编译成功！
        echo 运行程序: java -cp ".;lib/*" SOCAnalyzer
    ) else (
        echo 编译失败！
        pause
        exit /b 1
    )
) else (
    echo 使用Maven构建项目...
    mvn clean compile package
    
    if %errorlevel% equ 0 (
        echo 构建成功！
        echo 可执行JAR文件: target\soc-analyzer-1.0.0.jar
        echo 运行程序: java -jar target\soc-analyzer-1.0.0.jar
    ) else (
        echo 构建失败！
        pause
        exit /b 1
    )
)

echo ========================================
echo 构建完成！
pause
